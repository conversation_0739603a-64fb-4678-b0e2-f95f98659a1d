'use client';

import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Linkedin, Twitter, Mail, MapPin, Users, Award, Target } from 'lucide-react';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface TeamMember {
  id: string;
  name: string;
  position: string;
  imageUrl: string;
  bio: string;
  linkedin?: string;
  twitter?: string;
  email?: string;
  order: number;
  active: boolean;
}

export default function AboutPage() {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const heroRef = useRef<HTMLDivElement>(null);
  const teamRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);

  // Fetch team members from Firebase
  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        // First try with orderBy, if it fails, try without
        let querySnapshot: any;
        try {
          const q = query(
            collection(db, 'teamMembers'),
            where('active', '==', true),
            orderBy('order', 'asc')
          );
          querySnapshot = await getDocs(q);
        } catch (indexError) {
          console.log('Composite index not available, fetching without orderBy');
          // Fallback: fetch active team members without orderBy
          const q = query(
            collection(db, 'teamMembers'),
            where('active', '==', true)
          );
          querySnapshot = await getDocs(q);
        }

        const members = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as TeamMember[];

        // Sort manually if needed
        const sortedMembers = members.sort((a, b) => (a.order || 0) - (b.order || 0));
        setTeamMembers(sortedMembers);
        console.log('Fetched team members for about page:', sortedMembers);
      } catch (error) {
        console.error('Error fetching team members:', error);
        setTeamMembers([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero section animation
      gsap.fromTo(heroRef.current, 
        { opacity: 0, y: 50 },
        { opacity: 1, y: 0, duration: 1, ease: "power2.out" }
      );

      // Stats animation
      gsap.fromTo(statsRef.current?.children || [], 
        { opacity: 0, y: 30 },
        { 
          opacity: 1, 
          y: 0, 
          duration: 0.8, 
          stagger: 0.2,
          scrollTrigger: {
            trigger: statsRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Team cards animation
      gsap.fromTo(teamRef.current?.children || [], 
        { opacity: 0, y: 50, scale: 0.9 },
        { 
          opacity: 1, 
          y: 0, 
          scale: 1,
          duration: 0.6, 
          stagger: 0.1,
          scrollTrigger: {
            trigger: teamRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );
    });

    return () => ctx.revert();
  }, []);

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section ref={heroRef} className="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20 lg:py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            <Badge variant="outline" className="mb-6 px-4 py-2  bg-black text-white text-xl font-medium">
              About KaleidoNex
            </Badge>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              The minds behind
              <span className="text-blue-600"> Our Success </span>
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-8 leading-relaxed max-w-3xl mx-auto">
              We're a passionate team of designers, developers, and innovators dedicated to creating 
              premium templates that empower businesses to build beautiful digital experiences.
            </p>
            <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-500">
             
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      {/* <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div ref={statsRef} className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">1000+</div>
              <div className="text-gray-600">Premium Templates</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-green-600 mb-2">50K+</div>
              <div className="text-gray-600">Happy Customers</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-600 mb-2">99%</div>
              <div className="text-gray-600">Customer Satisfaction</div>
            </div>
          </div>
        </div>
      </section> */}

      {/* Team Section */}
      <section className="py-10 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* <div className="text-center mb-16"> */}
             <div className="text-center mb-8 ">
            <Badge variant="outline" className="mb-4 bg-black px-6 py-3 text-lg rounded-lg text-white ">
              Our Team
            </Badge>
            {/* <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Meet the People Behind KaleidoNex
            </h2> */}
            {/* <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our diverse team of experts brings together creativity, technical excellence, 
              and business acumen to deliver exceptional results.
            </p> */}
          </div>

          <div ref={teamRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {isLoading ? (
              // Loading skeleton
              Array.from({ length: 6 }).map((_, index) => (
                <Card key={index} className="animate-pulse">
                  <CardContent className="p-0">
                    <div className="aspect-square bg-gray-200"></div>
                    <div className="p-6 space-y-3">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      <div className="h-3 bg-gray-200 rounded w-full"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : teamMembers.length > 0 ? (
              teamMembers.map((member) => (
              <Card key={member.id} className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg overflow-hidden bg-white rounded-xl">
                <CardContent className="p-0">
                  {/* Profile Image - 4:4 aspect ratio */}
                  <div className="aspect-square relative overflow-hidden bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
                    <Image
                      src={member.imageUrl}
                      alt={member.name}
                      width={400}
                      height={400}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <p className="text-blue-600 font-semibold text-sm uppercase tracking-wide">
                        {member.position}
                      </p>
                    </div>
                    <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3">
                      {member.bio}
                    </p>

                    {/* Social Links - Always visible */}
                    <div className="flex justify-center gap-3 pt-4 border-t border-gray-100">
                      {member.linkedin && (
                        <a
                          href={member.linkedin}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors cursor-pointer"
                        >
                          <Linkedin className="h-4 w-4 text-blue-600" />
                        </a>
                      )}
                      {member.twitter && (
                        <a
                          href={member.twitter}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors cursor-pointer"
                        >
                          <Twitter className="h-4 w-4 text-blue-600" />
                        </a>
                      )}
                      {member.email && (
                        <a
                          href={`mailto:${member.email}`}
                          className="p-2 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors cursor-pointer"
                        >
                          <Mail className="h-4 w-4 text-blue-600" />
                        </a>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No team members found</h3>
                {/* <p className="text-gray-600">Team members will appear here when added by admin.</p> */}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      {/* <section className="py-20 bg-gradient-to-br from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <Target className="h-16 w-16 mx-auto mb-6 text-blue-200" />
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Our Mission</h2>
            <p className="text-xl leading-relaxed text-blue-100">
              To democratize high-quality design by providing premium templates that enable
              businesses of all sizes to create professional, beautiful digital experiences
              without the need for extensive design resources.
            </p>
          </div>
        </div>
      </section> */}
    </div>
  );
}
