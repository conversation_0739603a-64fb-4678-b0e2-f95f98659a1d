'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, Award } from 'lucide-react';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Project } from '@/types/project';

export default function WorkPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [categories, setCategories] = useState<string[]>(['All']);

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      console.log('Fetching projects from Firebase...');
      
      // Try with orderBy first
      let projectsData: Project[] = [];
      try {
        const projectsQuery = query(
          collection(db, 'projects'),
          where('status', '==', 'active'),
          orderBy('order', 'asc')
        );
        const querySnapshot = await getDocs(projectsQuery);
        projectsData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        })) as Project[];
      } catch (indexError) {
        console.log('OrderBy failed, trying without orderBy');
        // Fallback: fetch without orderBy
        const projectsQuery = query(
          collection(db, 'projects'),
          where('status', '==', 'active')
        );
        const querySnapshot = await getDocs(projectsQuery);
        projectsData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        })) as Project[];
        
        // Sort manually
        projectsData.sort((a, b) => (a.order || 0) - (b.order || 0));
      }

      console.log('Fetched projects:', projectsData.length);
      setProjects(projectsData);

      // Extract unique categories
      const uniqueCategories = ['All', ...new Set(projectsData.map(p => p.category))];
      setCategories(uniqueCategories);
    } catch (error) {
      console.error('Error fetching projects:', error);
      
      // Fallback to mock data
      const mockProjects: Project[] = [
        {
          id: '1',
          title: 'Cursor',
          description: 'A code editor powered by AI that helps write code faster and better. Build software faster in an IDE designed for pair-programming with AI.',
          category: 'Development Tools',
          images: [
            'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=600&h=400&fit=crop',
            'https://images.unsplash.com/photo-1517180102446-f3ece451e9d8?w=600&h=400&fit=crop'
          ],
          livePreviewUrl: 'https://cursor.sh',
          technologies: ['AI', 'Code Editor', 'TypeScript'],
          featured: true,
          order: 1,
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'admin'
        },
        {
          id: '2',
          title: 'Moonbeam',
          description: 'Never Write From Scratch Again. Kickstart your next great document with Moonbeam - Your long form AI Writing Assistant.',
          category: 'AI Writing',
          images: [
            'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=400&fit=crop',
            'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop'
          ],
          livePreviewUrl: 'https://moonbeam.ai',
          technologies: ['AI', 'Writing', 'Document'],
          featured: true,
          order: 2,
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'admin'
        }
      ];
      
      setProjects(mockProjects);
      setCategories(['All', 'Development Tools', 'AI Writing']);
    } finally {
      setLoading(false);
    }
  };

  const filteredProjects = projects.filter(project => 
    selectedCategory === 'All' || project.category === selectedCategory
  );



  if (loading) {
    return (
      <div className="container mx-auto px-4 py-20">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading our amazing work...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Hero Section */}
      <section className="py-16 sm:py-20 lg:py-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            {/* <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mb-6">
              <Award className="h-8 w-8 text-white" />
            </div> */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              A glimpse into our <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">projects</span>
            </h1>
            <p className="text-xl sm:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              A look at some of the amazing webapps that we've built recently.
            </p>
          </div>



          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-3 mb-12">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                onClick={() => setSelectedCategory(category)}
                className="rounded-full"
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="pb-16 sm:pb-20 lg:pb-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 gap-8 lg:gap-12">
            {filteredProjects.map((project, index) => (
              <div key={project.id} className="group">
                <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 border-0 shadow-lg">
                  <div className="flex flex-col lg:flex-row">
                    {/* Project Images - First on mobile, second on desktop */}
                    <div className="w-full lg:w-3/5 relative lg:order-2">
                      <div className="aspect-square lg:aspect-auto lg:h-full relative overflow-hidden bg-gray-50 rounded-lg">
                        {project.images.length >= 2 ? (
                          <>
                            {/* Desktop: 2 images side by side */}
                            <div className="hidden lg:grid grid-cols-2 gap-2 h-full p-2">
                              {project.images.slice(0, 2).map((image, imageIndex) => (
                                <div key={imageIndex} className="relative overflow-hidden rounded-xl">
                                  <img
                                    src={image}
                                    alt={`${project.title} - Image ${imageIndex + 1}`}
                                    className="lg:w-full lg:h-full lg:object-cover lg:rounded-md transition duration-300 blur-0 object-cover object-left-top rounded-xl aspect-square h-full w-full"
                                  />
                                </div>
                              ))}
                            </div>
                            {/* Mobile: 2 images stacked vertically */}
                            <div className="lg:hidden flex flex-col gap-2 h-full p-2">
                              {project.images.slice(0, 2).map((image, imageIndex) => (
                                <div key={imageIndex} className="relative overflow-hidden rounded-xl flex-1">
                                  <img
                                    src={image}
                                    alt={`${project.title} - Image ${imageIndex + 1}`}
                                    className="w-full h-32 object-cover object-left-top rounded-xl transition duration-300 blur-0"
                                  />
                                </div>
                              ))}
                            </div>
                          </>
                        ) : (
                          <div className="h-full p-2 bg-gray-100 rounded-xl">
                            <img
                              src={project.images[0] || 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop'}
                              alt={project.title}
                              className="w-full h-64 lg:h-full object-contain rounded-xl transition duration-300 blur-0"
                            />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Project Info - Second on mobile, first on desktop */}
                    <div className="w-full lg:w-2/5 p-8 lg:p-10 flex flex-col justify-center lg:order-1">
                      <div className="mb-6">
                        <Badge variant="secondary" className="mb-4">
                          {project.category}
                        </Badge>
                        <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
                          {project.title}
                        </h3>
                        <p className="text-gray-600 leading-relaxed mb-6">
                          {project.description}
                        </p>
                        <div className="flex flex-wrap gap-2 mb-6">
                          {project.technologies.map((tech, techIndex) => (
                            <Badge key={techIndex} variant="outline" className="text-xs">
                              {tech}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      {project.livePreviewUrl && (
                        <Button asChild className="w-fit">
                          <a href={project.livePreviewUrl} target="_blank" rel="noopener noreferrer">
                            Live Preview
                            <ExternalLink className="ml-2 h-4 w-4" />
                          </a>
                        </Button>
                      )}
                    </div>
                  </div>
                </Card>
              </div>
            ))}
          </div>

          {filteredProjects.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Award className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No projects found</h3>
              <p className="text-gray-600">
                {selectedCategory === 'All' 
                  ? 'No projects available at the moment.' 
                  : `No projects found in "${selectedCategory}" category.`
                }
              </p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
