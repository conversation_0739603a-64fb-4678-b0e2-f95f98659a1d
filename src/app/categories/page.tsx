'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Monitor, 
  ShoppingCart, 
  Briefcase, 
  Users, 
  FileText, 
  Smartphone,
  ArrowRight,
  Globe,
  Newspaper
} from 'lucide-react';

// Mock data for categories
const categories = [
  {
    id: '1',
    name: 'Dashboard',
    description: 'Admin panels, analytics dashboards, and data visualization templates',
    icon: Monitor,
    templateCount: 45,
    color: 'bg-blue-500',
    bgColor: 'bg-blue-50',
    textColor: 'text-blue-600'
  },
  {
    id: '2',
    name: 'E-commerce',
    description: 'Online stores, shopping carts, product catalogs, and marketplace templates',
    icon: ShoppingCart,
    templateCount: 32,
    color: 'bg-green-500',
    bgColor: 'bg-green-50',
    textColor: 'text-green-600'
  },
  {
    id: '3',
    name: 'Portfolio',
    description: 'Creative showcases for designers, developers, and creative professionals',
    icon: Briefcase,
    templateCount: 28,
    color: 'bg-purple-500',
    bgColor: 'bg-purple-50',
    textColor: 'text-purple-600'
  },
  {
    id: '4',
    name: 'Landing Page',
    description: 'High-converting marketing pages, product launches, and promotional sites',
    icon: FileText,
    templateCount: 56,
    color: 'bg-orange-500',
    bgColor: 'bg-orange-50',
    textColor: 'text-orange-600'
  },
  {
    id: '5',
    name: 'Corporate',
    description: 'Business websites, company profiles, and professional service sites',
    icon: Users,
    templateCount: 23,
    color: 'bg-indigo-500',
    bgColor: 'bg-indigo-50',
    textColor: 'text-indigo-600'
  },
  {
    id: '6',
    name: 'Mobile App',
    description: 'Mobile application UI kits, app landing pages, and mobile-first designs',
    icon: Smartphone,
    templateCount: 19,
    color: 'bg-pink-500',
    bgColor: 'bg-pink-50',
    textColor: 'text-pink-600'
  },
  {
    id: '7',
    name: 'Blog/CMS',
    description: 'Blog themes, content management systems, and publishing platforms',
    icon: Newspaper,
    templateCount: 34,
    color: 'bg-yellow-500',
    bgColor: 'bg-yellow-50',
    textColor: 'text-yellow-600'
  },
  {
    id: '8',
    name: 'Web App',
    description: 'SaaS applications, productivity tools, and web-based software interfaces',
    icon: Globe,
    templateCount: 41,
    color: 'bg-teal-500',
    bgColor: 'bg-teal-50',
    textColor: 'text-teal-600'
  }
];

export default function CategoriesPage() {
  const totalTemplates = categories.reduce((sum, category) => sum + category.templateCount, 0);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Template Categories
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-6">
          Explore our organized collection of premium templates across different categories and industries.
        </p>
        <div className="flex items-center justify-center space-x-6 text-sm text-gray-600">
          <div className="flex items-center">
            <span className="font-semibold text-2xl text-gray-900">{categories.length}</span>
            <span className="ml-2">Categories</span>
          </div>
          <div className="w-px h-6 bg-gray-300"></div>
          <div className="flex items-center">
            <span className="font-semibold text-2xl text-gray-900">{totalTemplates}</span>
            <span className="ml-2">Total Templates</span>
          </div>
        </div>
      </div>

      {/* Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {categories.map((category) => {
          const IconComponent = category.icon;
          return (
            <Link key={category.id} href={`/templates?category=${category.name}`}>
              <Card className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg h-full cursor-pointer overflow-hidden">
                <CardContent className="p-0">
                  {/* Header with Icon */}
                  <div className={`${category.bgColor} p-6 text-center`}>
                    <div className={`inline-flex items-center justify-center w-16 h-16 ${category.color} rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>
                    <h3 className={`text-xl font-semibold ${category.textColor} mb-2 group-hover:text-opacity-80 transition-colors`}>
                      {category.name}
                    </h3>
                    <Badge variant="secondary" className="bg-white/80">
                      {category.templateCount} templates
                    </Badge>
                  </div>
                  
                  {/* Content */}
                  <div className="p-6">
                    <p className="text-gray-600 text-sm leading-relaxed mb-4">
                      {category.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900">
                        Explore Templates
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-gray-600 group-hover:translate-x-1 transition-all" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          );
        })}
      </div>

      {/* Popular Categories Section */}
      <div className="mt-16">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Most Popular Categories
          </h2>
          <p className="text-gray-600">
            Categories with the highest number of downloads and user engagement
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          {categories
            .sort((a, b) => b.templateCount - a.templateCount)
            .slice(0, 3)
            .map((category, index) => {
              const IconComponent = category.icon;
              return (
                <Card key={category.id} className="relative overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className={`p-3 ${category.bgColor} rounded-lg`}>
                        <IconComponent className={`h-6 w-6 ${category.textColor}`} />
                      </div>
                      <Badge className="bg-gradient-to-r from-blue-600 to-purple-600">
                        #{index + 1} Popular
                      </Badge>
                    </div>
                    
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {category.name}
                    </h3>
                    
                    <p className="text-gray-600 text-sm mb-4">
                      {category.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">
                        {category.templateCount} templates available
                      </span>
                      <Link 
                        href={`/templates?category=${category.name}`}
                        className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center"
                      >
                        Browse
                        <ArrowRight className="h-3 w-3 ml-1" />
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
        </div>
      </div>

      {/* CTA Section */}
      <div className="mt-16 text-center">
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Can't Find What You're Looking For?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Our expert team can create custom templates tailored to your specific needs and industry requirements.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/custom-request"
              className="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors"
            >
              Request Custom Design
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
            <Link 
              href="/contact"
              className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors"
            >
              Contact Support
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
