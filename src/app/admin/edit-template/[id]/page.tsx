import React from 'react';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import EditTemplateClient from './EditTemplateClient';

// This function will be used to generate static paths
export async function generateStaticParams() {
  const templatesSnapshot = await getDocs(collection(db, 'templates'));
  return templatesSnapshot.docs.map(doc => ({
    id: doc.id
  }));
}

export default function EditTemplatePage() {
  return <EditTemplateClient />;
}
