'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MapPin,
  Clock,
  Users,
  GraduationCap,
  Star,
  Code,
  Palette,
  TrendingUp,
  Mail,
  ExternalLink,
  Calendar,
  Award,
  Loader2
} from 'lucide-react';
import Link from 'next/link';
import { getInternships, Internship } from '@/lib/firebaseServices';
import { toast } from 'sonner';



const benefits = [
  {
    icon: <GraduationCap className="h-6 w-6 text-blue-500" />,
    title: 'Learn & Grow',
    description: 'Hands-on experience with cutting-edge technologies and mentorship from senior developers'
  },
  {
    icon: <Users className="h-6 w-6 text-green-500" />,
    title: 'Team Collaboration',
    description: 'Work alongside experienced professionals and contribute to real projects'
  },
  {
    icon: <Award className="h-6 w-6 text-purple-500" />,
    title: 'Certificate & References',
    description: 'Completion certificate and strong references for future opportunities'
  },
  {
    icon: <TrendingUp className="h-6 w-6 text-orange-500" />,
    title: 'Career Path',
    description: 'Potential for full-time offers based on performance and company needs'
  }
];

const requirements = [
  'Currently enrolled in a relevant degree program or recent graduate',
  'Basic knowledge of the field you\'re applying for',
  'Strong communication skills and eagerness to learn',
  'Ability to commit to the full internship duration',
  'Portfolio or projects (for technical roles)',
  'Passion for technology and digital innovation'
];

export default function InternshipsPage() {
  const [internships, setInternships] = useState<Internship[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchInternships();
  }, []);

  const fetchInternships = async () => {
    try {
      setLoading(true);
      const fetchedInternships = await getInternships();
      setInternships(fetchedInternships);
    } catch (error) {
      console.error('Error fetching internships:', error);
      toast.error('Failed to load internship programs');
    } finally {
      setLoading(false);
    }
  };

  const handleApply = (internshipTitle: string) => {
    const subject = `Application for ${internshipTitle}`;
    const body = `Dear Internship Team,\n\nI am interested in applying for the ${internshipTitle} program at KaleidoneX.\n\nPlease find my resume and portfolio attached.\n\nBest regards,`;
    window.location.href = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white">
        <div className="container mx-auto px-4 py-20">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Internship Programs
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-green-100">
              Launch your career with hands-on experience at KaleidoneX
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <div className="flex items-center space-x-2">
                <Calendar className="h-5 w-5" />
                <span>3-6 Month Programs</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-5 w-5" />
                <span>Remote & Hybrid Options</span>
              </div>
              <div className="flex items-center space-x-2">
                <Award className="h-5 w-5" />
                <span>Paid Internships</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        {/* Why Intern With Us */}
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Intern With Us?</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-12">
            Get real-world experience, learn from industry experts, and kickstart your career in tech.
          </p>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="pt-6">
                  <div className="flex justify-center mb-4">
                    {benefit.icon}
                  </div>
                  <h3 className="font-semibold text-lg mb-2">{benefit.title}</h3>
                  <p className="text-gray-600 text-sm">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Available Programs */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Available Programs</h2>
            <p className="text-xl text-gray-600">
              Choose the program that matches your interests and skills
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-green-600" />
              <span className="ml-2 text-gray-600">Loading internship programs...</span>
            </div>
          ) : internships.length > 0 ? (
            <div className="grid gap-6">
              {internships.map((internship) => (
                <Card key={internship.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                      <div>
                        <CardTitle className="text-xl mb-2">{internship.title}</CardTitle>
                        <div className="flex flex-wrap gap-2 mb-2">
                          <Badge variant="outline">{internship.department}</Badge>
                          <Badge variant="outline">{internship.type}</Badge>
                          <Badge variant="outline">{internship.duration}</Badge>
                          <Badge variant="outline">{internship.level}</Badge>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-semibold text-green-600 mb-2">
                          {internship.stipend}
                        </div>
                        <Button onClick={() => handleApply(internship.title)}>
                          Apply Now
                          <ExternalLink className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 mb-4">{internship.description}</p>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-semibold mb-2">Skills You'll Learn:</h4>
                        <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                          {internship.skills.map((skill, index) => (
                            <li key={index}>{skill}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2">Projects You'll Work On:</h4>
                        <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                          {internship.projects.map((project, index) => (
                            <li key={index}>{project}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <GraduationCap className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Active Programs</h3>
              <p className="text-gray-600 mb-4">
                We don't have any active internship programs at the moment, but we're always looking for talented students.
              </p>
              <Button onClick={() => handleApply('General Internship Application')}>
                Express Interest
                <Mail className="ml-2 h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Requirements */}
        <div className="mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">General Requirements</CardTitle>
              <CardDescription>
                What we look for in our interns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {requirements.map((requirement, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <Star className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{requirement}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Application Process */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Application Process</h2>
          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 font-bold">1</span>
              </div>
              <h3 className="font-semibold mb-2">Apply Online</h3>
              <p className="text-sm text-gray-600">Submit your application with resume and portfolio</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-green-600 font-bold">2</span>
              </div>
              <h3 className="font-semibold mb-2">Initial Review</h3>
              <p className="text-sm text-gray-600">We review your application and portfolio</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-purple-600 font-bold">3</span>
              </div>
              <h3 className="font-semibold mb-2">Interview</h3>
              <p className="text-sm text-gray-600">Virtual interview with our team</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-orange-600 font-bold">4</span>
              </div>
              <h3 className="font-semibold mb-2">Start Internship</h3>
              <p className="text-sm text-gray-600">Begin your journey with us!</p>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-0">
          <CardContent className="text-center py-12">
            <Mail className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Start Your Journey?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Have questions about our internship programs? We'd love to hear from you and help you find the perfect opportunity.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" onClick={() => handleApply('General Internship Application')}>
                <Mail className="mr-2 h-5 w-5" />
                Apply for Internship
              </Button>
              <Button size="lg" variant="outline" onClick={() => {
                const subject = 'Internship Program Inquiry';
                const body = 'Dear Internship Team,\n\nI have some questions about your internship programs.\n\nBest regards,';
                window.location.href = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
              }}>
                Ask Questions
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
