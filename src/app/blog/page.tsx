'use client';

import { useEffect } from 'react';

export default function BlogPage() {
  useEffect(() => {
    // Redirect to external blog website
    window.location.href = 'https://blog.kaleidonex.com'; // Replace with your actual blog URL
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
      <div className="text-center max-w-md mx-auto px-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-6"></div>
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Redirecting to Blog...</h1>
        <p className="text-gray-600 mb-6">
          You're being redirected to our blog website. If you're not redirected automatically,
          <a
            href="https://blog.kaleidonex.com"
            className="text-blue-600 hover:text-blue-800 underline ml-1"
          >
            click here
          </a>.
        </p>
      </div>
    </div>
  );
}
