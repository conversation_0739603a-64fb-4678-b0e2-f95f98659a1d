'use client';

import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Home, Search } from 'lucide-react';

export default function NotFound() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center px-4">
      <div className="max-w-lg w-full text-center">
        {/* 404 Animation */}
        <div className="mb-8">
          <div className="text-8xl md:text-9xl font-bold text-blue-600 mb-4 animate-pulse">
            404
          </div>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto rounded-full"></div>
        </div>

        {/* Error Message */}
        <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
          Oops! Page Not Found
        </h1>
        <p className="text-gray-600 mb-8 text-lg leading-relaxed">
          The page you're looking for doesn't exist or has been moved.
          Don't worry, let's get you back on track!
        </p>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild size="lg" className="px-6 py-3">
            <Link href="/" className="flex items-center">
              <Home className="w-4 h-4 mr-2" />
              Go Home
            </Link>
          </Button>
          <Button
            variant="outline"
            size="lg"
            onClick={() => router.back()}
            className="px-6 py-3 flex items-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </Button>
          <Button asChild variant="secondary" size="lg" className="px-6 py-3">
            <Link href="/templates" className="flex items-center">
              <Search className="w-4 h-4 mr-2" />
              Browse Templates
            </Link>
          </Button>
        </div>

        {/* Helpful Links */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500 mb-4">Looking for something specific?</p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Link href="/templates" className="text-blue-600 hover:text-blue-800 transition-colors">
              Templates
            </Link>
            <Link href="/about" className="text-blue-600 hover:text-blue-800 transition-colors">
              About Us
            </Link>
            <Link href="/contact" className="text-blue-600 hover:text-blue-800 transition-colors">
              Contact
            </Link>
            <Link href="/custom-request" className="text-blue-600 hover:text-blue-800 transition-colors">
              Custom Request
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}