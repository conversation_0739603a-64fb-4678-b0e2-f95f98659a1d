'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import Link from 'next/link';
import {
  ArrowLeft,
  Package,
  Calendar,
  IndianRupee,
  Download,
  ExternalLink,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Mail,
  Phone,
  User
} from 'lucide-react';
import {
  getContactMessages,
  subscribeToContactMessages,
  getCustomRequests,
  subscribeToCustomRequests
} from '@/lib/firebaseServices';
import { ContactMessage, CustomRequest } from '@/types';

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case 'approved':
      return <CheckCircle className="h-4 w-4 text-blue-600" />;
    case 'confirmed':
      return <Clock className="h-4 w-4 text-blue-600" />;
    case 'pending':
      return <AlertCircle className="h-4 w-4 text-yellow-600" />;
    case 'declined':
      return <XCircle className="h-4 w-4 text-red-600" />;
    default:
      return <Package className="h-4 w-4 text-gray-600" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'approved':
      return 'bg-blue-100 text-blue-800';
    case 'confirmed':
      return 'bg-blue-100 text-blue-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'declined':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return 'Pending Review';
    case 'confirmed':
      return 'Confirmed';
    case 'approved':
      return 'Order Approved';
    case 'completed':
      return 'Completed';
    case 'declined':
      return 'Declined';
    default:
      return status.charAt(0).toUpperCase() + status.slice(1);
  }
};

export default function OrdersPage() {
  const { user, userData } = useAuth();
  const [purchaseRequests, setPurchaseRequests] = useState<ContactMessage[]>([]);
  const [customRequests, setCustomRequests] = useState<CustomRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchData = async () => {
      if (!user) return;

      try {
        setLoading(true);

        // Fetch purchase requests
        const messages = await getContactMessages();
        const userPurchaseRequests = messages.filter(msg =>
          msg.type === 'purchase-request' && msg.userEmail === user.email
        );
        setPurchaseRequests(userPurchaseRequests);

        // Fetch custom requests
        const customReqs = await getCustomRequests();
        const userCustomRequests = customReqs.filter(req => req.userEmail === user.email);
        setCustomRequests(userCustomRequests);

      } catch (error: unknown) {
        console.error('Error fetching orders:', error);
        setError('Failed to load your orders');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchData();

      // Set up real-time listeners
      const unsubscribeMessages = subscribeToContactMessages((messages) => {
        const userPurchaseRequests = messages.filter(msg =>
          msg.type === 'purchase-request' && msg.userEmail === user.email
        );
        setPurchaseRequests(userPurchaseRequests);
      });

      const unsubscribeCustom = subscribeToCustomRequests((customReqs) => {
        const userCustomRequests = customReqs.filter(req => req.userEmail === user.email);
        setCustomRequests(userCustomRequests);
      });

      return () => {
        unsubscribeMessages();
        unsubscribeCustom();
      };
    }
  }, [user]);

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-2xl font-bold mb-4">Please Sign In</h1>
        <p className="text-gray-600 mb-4">You need to be signed in to view your orders.</p>
        <Button asChild>
          <Link href="/auth">Sign In</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Button asChild variant="outline" size="sm">
              <Link href="/">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Home
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            My Orders & Requests
          </h1>
          <p className="text-gray-600">
            Track your template purchases and custom design requests
          </p>
        </div>

        {/* Error State */}
        {error && (
          <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading your orders...</p>
            </div>
          </div>
        )}

        {/* Orders List */}
        {!loading && (
          <div className="space-y-8">
            {/* Purchase Requests Section */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Template Purchase Requests</h2>
              {purchaseRequests.length > 0 ? (
              purchaseRequests.map((request) => (
                <Card key={request.id} className="overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row lg:items-start gap-6">
                      {/* Request Details */}
                      <div className="flex-1 space-y-4">
                        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900">
                              {request.subject}
                            </h3>
                            {request.templateTitle && (
                              <p className="text-sm text-blue-600 mt-1">
                                Template: {request.templateTitle}
                              </p>
                            )}
                            <div className="flex items-center gap-2 mt-2">
                              {getStatusIcon(request.status)}
                              <Badge className={getStatusColor(request.status)}>
                                {getStatusText(request.status)}
                              </Badge>
                            </div>
                          </div>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-medium text-gray-900 mb-2">Request Message:</h4>
                          <p className="text-sm text-gray-700 whitespace-pre-wrap">{request.message}</p>
                        </div>

                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            Submitted on {new Date(request.createdAt.seconds ? request.createdAt.seconds * 1000 : request.createdAt).toLocaleDateString()}
                          </div>
                          <div className="flex items-center gap-1">
                            <Package className="h-4 w-4" />
                            Request #{request.id.slice(-6)}
                          </div>
                        </div>

                        {/* Status Information */}
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <h4 className="font-medium text-blue-900 mb-2">Status Information:</h4>
                          <div className="text-sm text-blue-800">
                            {request.status === 'pending' && (
                              <p>Your purchase request is being reviewed by our team. We'll contact you soon with more details.</p>
                            )}
                            {request.status === 'confirmed' && (
                              <p>Your request has been confirmed! We're preparing your order details and will send you payment information shortly.</p>
                            )}
                            {request.status === 'approved' && (
                              <p>Your order has been approved! Please proceed with the payment to complete your purchase.</p>
                            )}
                            {request.status === 'completed' && (
                              <p>Your order is complete! You should have received the template files and documentation.</p>
                            )}
                            {request.status === 'declined' && (
                              <p>Unfortunately, we couldn't process your request at this time. Please contact us for more information.</p>
                            )}
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex flex-wrap gap-2 pt-2">
                          {request.status === 'pending' && (
                            <Button size="sm" variant="outline" disabled>
                              <Clock className="mr-2 h-4 w-4" />
                              Awaiting Review
                            </Button>
                          )}
                          {request.status === 'confirmed' && (
                            <Button size="sm" variant="outline" disabled>
                              <AlertCircle className="mr-2 h-4 w-4" />
                              Preparing Order Details
                            </Button>
                          )}
                          {request.status === 'approved' && (
                            <Button size="sm" variant="default">
                              <IndianRupee className="mr-2 h-4 w-4" />
                              Proceed to Payment
                            </Button>
                          )}
                          {request.status === 'completed' && (
                            <Button size="sm" variant="default">
                              <Download className="mr-2 h-4 w-4" />
                              Download Files
                            </Button>
                          )}
                          <Button size="sm" variant="outline" asChild>
                            <Link href="/contact">
                              <Mail className="mr-2 h-4 w-4" />
                              Contact Support
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
              ) : (
                <Card>
                  <CardContent className="text-center py-8">
                    <Package className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                    <p className="text-gray-600">No template purchase requests yet.</p>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Custom Requests Section */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Custom Design Requests</h2>
              {customRequests.length > 0 ? (
                <div className="space-y-4">
                  {customRequests.map((request) => (
                    <Card key={request.id} className="overflow-hidden">
                      <CardContent className="p-6">
                        <div className="flex flex-col lg:flex-row lg:items-start gap-6">
                          <div className="flex-1 space-y-4">
                            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
                              <div>
                                <h3 className="text-lg font-semibold text-gray-900">
                                  {request.title}
                                </h3>
                                <p className="text-sm text-purple-600 mt-1">
                                  Category: {request.category}
                                </p>
                                <div className="flex items-center gap-2 mt-2">
                                  {getStatusIcon(request.status)}
                                  <Badge className={getStatusColor(request.status)}>
                                    {getStatusText(request.status)}
                                  </Badge>
                                  {request.paymentStatus && request.status === 'completed' && (
                                    <Badge className={request.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                                      {request.paymentStatus === 'paid' ? 'Payment Done' : 'Payment Pending'}
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </div>

                            <div className="bg-gray-50 p-4 rounded-lg">
                              <h4 className="font-medium text-gray-900 mb-2">Project Description:</h4>
                              <p className="text-sm text-gray-700 whitespace-pre-wrap">{request.description}</p>
                              {request.budget && (
                                <p className="text-sm text-green-600 mt-2 font-medium">
                                  Budget: ₹{request.budget.toLocaleString()}
                                </p>
                              )}
                            </div>

                            <div className="flex items-center gap-4 text-sm text-gray-600">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-4 w-4" />
                                Submitted on {new Date(request.createdAt.seconds ? request.createdAt.seconds * 1000 : request.createdAt).toLocaleDateString()}
                              </div>
                              <div className="flex items-center gap-1">
                                <Package className="h-4 w-4" />
                                Request #{request.id.slice(-6)}
                              </div>
                            </div>

                            {request.adminNotes && (
                              <div className="bg-blue-50 p-4 rounded-lg">
                                <h4 className="font-medium text-blue-900 mb-2">Admin Notes:</h4>
                                <p className="text-sm text-blue-800">{request.adminNotes}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="text-center py-8">
                    <Package className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                    <p className="text-gray-600">No custom design requests yet.</p>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Empty State for Both */}
            {purchaseRequests.length === 0 && customRequests.length === 0 && (
              <Card>
                <CardContent className="text-center py-12">
                  <Package className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No orders yet</h3>
                  <p className="text-gray-600 mb-4">
                    You haven't made any requests yet. Start by browsing templates or requesting a custom design.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-2 justify-center">
                    <Button asChild>
                      <Link href="/templates">Browse Templates</Link>
                    </Button>
                    <Button asChild variant="outline">
                      <Link href="/custom-request">Request Custom Design</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

      </div>
    </div>
  );
}
