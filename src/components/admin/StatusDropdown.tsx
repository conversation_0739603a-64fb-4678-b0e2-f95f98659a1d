'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import {
  ChevronDown,
  Clock,
  PlayCircle,
  CheckCircle,
  XCircle,
  MessageSquare,
  Loader2,
  CreditCard,
  DollarSign
} from 'lucide-react';

interface StatusDropdownProps {
  currentStatus: string;
  onStatusChange: (status: string) => Promise<void>;
  type: 'custom-request' | 'contact-message' | 'purchase-request';
  disabled?: boolean;
  paymentStatus?: string;
  onPaymentStatusChange?: (status: string) => Promise<void>;
}

const getStatusConfig = (status: string, type: 'custom-request' | 'contact-message' | 'purchase-request') => {
  if (type === 'custom-request') {
    switch (status) {
      case 'pending':
        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };
      case 'in-progress':
        return { icon: PlayCircle, label: 'In Progress', variant: 'default' as const, color: 'text-blue-600' };
      case 'completed':
        return { icon: CheckCircle, label: 'Completed', variant: 'secondary' as const, color: 'text-green-600' };
      case 'cancelled':
        return { icon: XCircle, label: 'Cancelled', variant: 'destructive' as const, color: 'text-red-600' };
      default:
        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };
    }
  } else if (type === 'purchase-request') {
    switch (status) {
      case 'pending':
        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };
      case 'confirmed':
        return { icon: CheckCircle, label: 'Confirmed', variant: 'secondary' as const, color: 'text-blue-600' };
      case 'approved':
        return { icon: CheckCircle, label: 'Order Approved', variant: 'default' as const, color: 'text-green-600' };
      case 'completed':
        return { icon: CheckCircle, label: 'Completed', variant: 'secondary' as const, color: 'text-green-600' };
      case 'declined':
        return { icon: XCircle, label: 'Declined', variant: 'destructive' as const, color: 'text-red-600' };
      default:
        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };
    }
  } else {
    switch (status) {
      case 'pending':
        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };
      case 'responded':
        return { icon: MessageSquare, label: 'Responded', variant: 'secondary' as const, color: 'text-blue-600' };
      case 'in-progress':
        return { icon: PlayCircle, label: 'In Progress', variant: 'default' as const, color: 'text-blue-600' };
      case 'resolved':
        return { icon: CheckCircle, label: 'Resolved', variant: 'secondary' as const, color: 'text-green-600' };
      case 'declined':
        return { icon: XCircle, label: 'Declined', variant: 'destructive' as const, color: 'text-red-600' };
      default:
        return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };
    }
  }
};

const getPaymentStatusConfig = (paymentStatus: string) => {
  switch (paymentStatus) {
    case 'pending':
      return { icon: Clock, label: 'Payment Pending', variant: 'outline' as const, color: 'text-yellow-600' };
    case 'paid':
      return { icon: CheckCircle, label: 'Payment Done', variant: 'secondary' as const, color: 'text-green-600' };
    case 'not-required':
      return { icon: DollarSign, label: 'No Payment Required', variant: 'default' as const, color: 'text-gray-600' };
    default:
      return { icon: Clock, label: 'Payment Pending', variant: 'outline' as const, color: 'text-yellow-600' };
  }
};

const getAvailableStatuses = (currentStatus: string, type: 'custom-request' | 'contact-message' | 'purchase-request') => {
  if (type === 'custom-request') {
    switch (currentStatus) {
      case 'pending':
        return [
          { value: 'in-progress', label: 'Accept & Start', icon: PlayCircle },
          { value: 'cancelled', label: 'Decline', icon: XCircle }
        ];
      case 'in-progress':
        return [
          { value: 'completed', label: 'Mark Complete', icon: CheckCircle }
        ];
      default:
        return [];
    }
  } else if (type === 'purchase-request') {
    switch (currentStatus) {
      case 'pending':
        return [
          { value: 'confirmed', label: 'Confirm Order', icon: CheckCircle },
          { value: 'declined', label: 'Decline', icon: XCircle }
        ];
      case 'confirmed':
        return [
          { value: 'approved', label: 'Approve Order', icon: CheckCircle },
          { value: 'declined', label: 'Decline', icon: XCircle }
        ];
      case 'approved':
        return [
          { value: 'completed', label: 'Mark Complete', icon: CheckCircle }
        ];
      default:
        return [];
    }
  } else {
    const allStatuses = [
      { value: 'responded', label: 'Respond', icon: MessageSquare },
      { value: 'in-progress', label: 'In Progress', icon: PlayCircle },
      { value: 'declined', label: 'Decline', icon: XCircle },
      { value: 'resolved', label: 'Resolve', icon: CheckCircle }
    ];

    // Filter out current status and resolved/declined if already set
    return allStatuses.filter(status => {
      if (status.value === currentStatus) return false;
      if ((currentStatus === 'resolved' || currentStatus === 'declined') &&
          (status.value === 'resolved' || status.value === 'declined')) return false;
      return true;
    });
  }
};

export default function StatusDropdown({
  currentStatus,
  onStatusChange,
  type,
  disabled = false,
  paymentStatus,
  onPaymentStatusChange
}: StatusDropdownProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [isUpdatingPayment, setIsUpdatingPayment] = useState(false);
  const statusConfig = getStatusConfig(currentStatus, type);
  const availableStatuses = getAvailableStatuses(currentStatus, type);
  const StatusIcon = statusConfig.icon;

  const paymentStatusConfig = paymentStatus ? getPaymentStatusConfig(paymentStatus) : null;
  const PaymentIcon = paymentStatusConfig?.icon;

  const handleStatusChange = async (newStatus: string) => {
    setIsUpdating(true);
    try {
      await onStatusChange(newStatus);
    } catch (error) {
      console.error('Error updating status:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handlePaymentStatusChange = async (newPaymentStatus: string) => {
    if (!onPaymentStatusChange) return;

    setIsUpdatingPayment(true);
    try {
      await onPaymentStatusChange(newPaymentStatus);
    } catch (error) {
      console.error('Error updating payment status:', error);
    } finally {
      setIsUpdatingPayment(false);
    }
  };

  if (availableStatuses.length === 0) {
    return (
      <div className="flex items-center gap-2">
        <Badge variant={statusConfig.variant} className="flex items-center gap-1">
          <StatusIcon className="h-3 w-3" />
          {statusConfig.label}
        </Badge>

        {/* Show payment status for completed custom requests */}
        {type === 'custom-request' && currentStatus === 'completed' && paymentStatusConfig && PaymentIcon && (
          <div className="flex items-center gap-2">
            <Badge variant={paymentStatusConfig.variant} className="flex items-center gap-1">
              <PaymentIcon className="h-3 w-3" />
              {paymentStatusConfig.label}
            </Badge>

            {paymentStatus === 'pending' && onPaymentStatusChange && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={disabled || isUpdatingPayment}
                    className="h-8 px-2"
                  >
                    {isUpdatingPayment ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <>
                        <span className="sr-only sm:not-sr-only">Payment</span>
                        <ChevronDown className="h-3 w-3 ml-1" />
                      </>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-40">
                  <DropdownMenuItem
                    onClick={() => handlePaymentStatusChange('paid')}
                    className="flex items-center gap-2 cursor-pointer"
                  >
                    <CheckCircle className="h-4 w-4" />
                    Mark Paid
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handlePaymentStatusChange('not-required')}
                    className="flex items-center gap-2 cursor-pointer"
                  >
                    <DollarSign className="h-4 w-4" />
                    No Payment Required
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <Badge variant={statusConfig.variant} className="flex items-center gap-1">
        <StatusIcon className="h-3 w-3" />
        {statusConfig.label}
      </Badge>
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            disabled={disabled || isUpdating}
            className="h-8 px-2"
          >
            {isUpdating ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <>
                <span className="sr-only sm:not-sr-only">Update</span>
                <ChevronDown className="h-3 w-3 ml-1" />
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-40">
          {availableStatuses.map((status) => {
            const Icon = status.icon;
            return (
              <DropdownMenuItem
                key={status.value}
                onClick={() => handleStatusChange(status.value)}
                className="flex items-center gap-2 cursor-pointer"
              >
                <Icon className="h-4 w-4" />
                {status.label}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
